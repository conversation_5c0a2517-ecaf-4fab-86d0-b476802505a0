import { useQuery } from '@tanstack/react-query'

import { makeQueryErrorHandlerWithToast } from 'api/helpers'
import { restGet } from 'src/api/rest-api-caller'
import { createQuery } from 'src/util-functions/react-query-utils'

export type JobInfo = {
  jobId: number
  status: string
  completionLevel: number
  description?: string
  statusDetails?: string
  link?: string
}

export declare namespace GetJobStatus {
  type ApiInput = {
    jobId: number
  }

  type ApiOutput = JobInfo
}

async function fetchJobStatus(params: GetJobStatus.ApiInput) {
  return restGet<GetJobStatus.ApiOutput>(`/job/${params.jobId}`)
}

export const jobStatusQuery = (jobId: number | null) =>
  createQuery({
    queryKey: ['job-status', jobId],
    queryFn: () => fetchJobStatus({ jobId: jobId as number }),
    refetchInterval: 1000, // Poll every 1 second
    enabled: jobId !== null,
    ...makeQueryErrorHandlerWithToast(),
  })

export default function useJobStatusQuery(jobId: number | null) {
  const query = useQuery(jobStatusQuery(jobId))

  // Stop polling when job is completed or failed
  const isCompleted =
    query.data?.status === 'Completed' || query.data?.status === 'Failed'

  if (isCompleted && query.isRefetching) {
    query.refetch()
  }

  return query
}
